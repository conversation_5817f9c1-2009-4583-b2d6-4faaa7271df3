export type ReportStatus = 'Documentos' | 'Finiquito' | 'Finalizado' | 'Pago';

export type StatusType = 'Vigente' | 'Vencida' | 'Aprobado' | 'Pendiente' | 'Rechazado' ;

export interface Report {
  poliza: string;
  numeroPoliza: string;
  estatusPoliza: StatusType;
  periodo: string;
  sumaAsegurada: string;
  remanenteSuma: string;
  estatusReporte: ReportStatus;
}

export interface TimelineStep {
  id: string;
  status: ReportStatus;
  label: string;
  isActive: boolean;
  isCompleted: boolean;
}

// Tipos para los endpoints de reports
export interface ReportListParams {
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'updatedAt';
  sortDir?: 'asc' | 'desc';
}

export interface ReportListResponse {
  data: ReportItem[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface ReportItem {
  id: number;
  fechaIncidente: string;
  lugarIncidente: string;
  totalReclamado: string;
  estatusReclamacion: string;
  producto: {
    id: number;
    name: string;
  };
  cobertura: {
    id: number;
    name: string;
  };
  poliza: {
    numeroPoliza: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Interfaz para mantener compatibilidad con código existente
export interface ReportItemFlat {
  id: string;
  fechaCreacion: string;
  fechaIncidente: string;
  tipoIncidente: string;
  numeroPoliza: string;
  resultado: string;
  cobertura: string;
  status: string;
}

export interface FiscalInfoRequest {
  ocupacionProfesionActividad: string;
  nombreEmpresaLaboral: string;
  numeroSerieCertificadoDigital: string;
  curp: string;
  rfc: string;
  claveCuentaBancaria: string;
  funcionarioGobiernoAltaJerarquia: boolean;
  conyugeDependienteEconomicoFuncionario: boolean;
  negocioPropioaccionistaSociedad: boolean;
  actuaNombreCuentaPropia: boolean;
  comprobanteDomicilio: string;
  constanciaCurp: string;
  constanciaSituacionFiscal: string;
  caratulaBancaria: string;
  identificacionOficial: string;
}

export interface AssociateDocsRequest {
  reclamacionId: string;
}

export interface UpdateReportStatusRequest {
  status: string;
}
